import { DOC_FRAGMENT_FIELED } from '@repo/editor-common';
import type { EditorExtensionOptions, ExtensionInitContent } from '@repo/ui-business-editor';
import {
  getCommonFunctionExtensionList,
  getMarkExtensionList,
  getNodeExtensionList,
  LocalCollaboration,
  SlashCommand,
} from '@repo/ui-business-editor';
import { AnyExtension } from '@tiptap/core';
import { Collaboration } from '@tiptap/extension-collaboration';
import { ImageUploadController } from '../editor-kit/extensions/image-upload-controller';
import { ThoughtAdapter } from '../editor-kit/extensions/thought-adpater-extension';
import { BusinessDiffTransformUtils } from '../editor-kit/extensions/diff-transform-utils';

export type ThoughtEditorExtensionConfg = {
  id: string;
  options?: EditorExtensionOptions;
  initContent: ExtensionInitContent;
};

export const getThoughtEditorExtensions = (config: ThoughtEditorExtensionConfg): AnyExtension[] => {
  const { options, initContent, id } = config;
  const { ydoc } = initContent;
  return [
    ...getCommonFunctionExtensionList({
      ...options,
      imageOptions: {
        ...options?.imageOptions,
        imageUploadController: new ImageUploadController(),
      },
      diffBlockOptions: {
        diffTransformUtils: new DiffTransformUtils(),
      },
    }),
    ...getMarkExtensionList(options),
    ...getNodeExtensionList(options),
    Collaboration.configure({
      document: ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
    ThoughtAdapter.configure({
      id,
    }),
    LocalCollaboration.configure({
      document: ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
    SlashCommand,
  ].filter(Boolean);
};
